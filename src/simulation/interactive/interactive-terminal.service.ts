import { Injectable, Logger } from '@nestjs/common';
import * as readline from 'readline';
import { Subject, Observable } from 'rxjs';

export interface TerminalPrompt {
  message: string;
  type: 'input' | 'confirm' | 'select';
  options?: string[];
  defaultValue?: string;
}

export interface TerminalResponse {
  value: string;
  confirmed: boolean;
}

@Injectable()
export class InteractiveTerminalService {
  private readonly log = new Logger(InteractiveTerminalService.name);
  private rl!: readline.Interface;
  private responseSubject = new Subject<TerminalResponse>();

  constructor() {
    this.initializeReadline();
  }

  private initializeReadline() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    // Handle Ctrl+C gracefully
    this.rl.on('SIGINT', () => {
      this.showMessage('\n👋 Simulation terminated by user');
      process.exit(0);
    });
  }

  showBanner() {
    console.clear();
    console.log(
      '╔══════════════════════════════════════════════════════════════╗',
    );
    console.log(
      '║              🎮 TEAM ARCADE INTERACTIVE SIMULATOR            ║',
    );
    console.log(
      '║                                                              ║',
    );
    console.log(
      '║  Manual control system for testing the 9-phase workflow     ║',
    );
    console.log(
      '║  Type "help" at any time for available commands             ║',
    );
    console.log(
      '║  Press Ctrl+C to exit                                       ║',
    );
    console.log(
      '╚══════════════════════════════════════════════════════════════╝',
    );
    console.log('');
  }

  showPhaseHeader(phase: number, title: string, description: string) {
    console.log('');
    console.log('═'.repeat(70));
    console.log(`🎯 PHASE ${phase}: ${title.toUpperCase()}`);
    console.log(`📋 ${description}`);
    console.log('═'.repeat(70));
    console.log('');
  }

  showStatus(
    status: string,
    color: 'green' | 'yellow' | 'red' | 'blue' | 'cyan' = 'cyan',
  ) {
    const colors = {
      green: '\x1b[32m',
      yellow: '\x1b[33m',
      red: '\x1b[31m',
      blue: '\x1b[34m',
      cyan: '\x1b[36m',
    };
    const reset = '\x1b[0m';
    console.log(`${colors[color]}📊 STATUS: ${status}${reset}`);
  }

  showMessage(
    message: string,
    type: 'info' | 'success' | 'warning' | 'error' = 'info',
  ) {
    const icons = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌',
    };
    console.log(`${icons[type]} ${message}`);
  }

  showOptions(options: string[]) {
    console.log('📋 Available options:');
    options.forEach((option, index) => {
      console.log(`   ${index + 1}. ${option}`);
    });
    console.log('');
  }

  async promptInput(message: string, defaultValue?: string): Promise<string> {
    return new Promise((resolve) => {
      const prompt = defaultValue
        ? `${message} (default: ${defaultValue}): `
        : `${message}: `;
      this.rl.question(prompt, (answer) => {
        resolve(answer.trim() || defaultValue || '');
      });
    });
  }

  async promptConfirm(
    message: string,
    defaultValue: boolean = false,
  ): Promise<boolean> {
    return new Promise((resolve) => {
      const defaultText = defaultValue ? 'Y/n' : 'y/N';
      this.rl.question(`${message} (${defaultText}): `, (answer) => {
        const response = answer.trim().toLowerCase();
        if (response === '') {
          resolve(defaultValue);
        } else {
          resolve(response === 'y' || response === 'yes');
        }
      });
    });
  }

  async promptSelect(message: string, options: string[]): Promise<number> {
    return new Promise((resolve) => {
      this.showOptions(options);
      this.rl.question(`${message} (1-${options.length}): `, (answer) => {
        const selection = parseInt(answer.trim(), 10);
        if (selection >= 1 && selection <= options.length) {
          resolve(selection - 1);
        } else {
          this.showMessage('Invalid selection. Please try again.', 'error');
          resolve(this.promptSelect(message, options));
        }
      });
    });
  }

  async waitForEnter(
    message: string = 'Press Enter to continue',
  ): Promise<void> {
    return new Promise((resolve) => {
      this.rl.question(`\n⏸️  ${message}... `, () => {
        resolve();
      });
    });
  }

  showHelp() {
    console.log('');
    console.log('🆘 HELP - Available Commands:');
    console.log('');
    console.log('  help           - Show this help message');
    console.log('  status         - Show current phase and system status');
    console.log('  skip           - Skip current phase (if applicable)');
    console.log('  emergency      - Trigger emergency stop');
    console.log('  restart        - Restart the simulation');
    console.log('  quit/exit      - Exit the simulation');
    console.log('');
    console.log('📝 Phase-specific commands will be shown during each phase.');
    console.log('');
  }

  showGameScoreInput() {
    console.log('🎮 ARCADE GAME SCORES INPUT');
    console.log('Enter scores for each of the 4 arcade games:');
    console.log(
      '(Enter 0 for games not played, or high scores for potential jackpots)',
    );
    console.log('');
  }

  showResultSummary(scores: any, result: any) {
    console.log('');
    console.log('📊 GAME RESULTS SUMMARY');
    console.log('═'.repeat(50));
    console.log(`🎮 Game 1 Score: ${scores.game1}`);
    console.log(`🎮 Game 2 Score: ${scores.game2}`);
    console.log(`🎮 Game 3 Score: ${scores.game3}`);
    console.log(`🎮 Game 4 Score: ${scores.game4}`);
    console.log(`📈 Total Score: ${result.totalScore}`);
    console.log(`🏆 Team Result: ${result.teamWon ? 'WON' : 'LOST'}`);
    console.log(`💰 Jackpot: ${result.hasJackpot ? 'YES!' : 'No'}`);
    console.log('═'.repeat(50));
    console.log('');
  }

  cleanup() {
    if (this.rl) {
      this.rl.close();
    }
  }
}
