import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Subject, Observable } from 'rxjs';
import { InteractiveTerminalService } from './interactive-terminal.service';
import { GameScores } from '../../hardware/interfaces/serial-control.interface';
import { TeamGameManagerResponse } from '../../common/dto/game‑manager.dto';

export interface SimulationEvent {
  type:
    | 'BADGE_SCAN'
    | 'ROOM_TIMER_EXPIRED'
    | 'ALL_GAMES_COMPLETE'
    | 'SCORES_RECEIVED'
    | 'EMERGENCY_STOP';
  data?: any;
}

export interface SimulationState {
  currentPhase: number;
  phaseTitle: string;
  isWaitingForInput: boolean;
  sessionActive: boolean;
  currentTeam?: TeamGameManagerResponse;
}

@Injectable()
export class SimulationControllerService implements OnModuleInit {
  private readonly log = new Logger(SimulationControllerService.name);

  private eventSubject = new Subject<SimulationEvent>();
  private badgeScannedSubject = new Subject<string>();
  private roomTimerExpiredSubject = new Subject<void>();
  private allGamesCompleteSubject = new Subject<void>();
  private scoresReceivedSubject = new Subject<GameScores>();

  private state: SimulationState = {
    currentPhase: 1,
    phaseTitle: 'Badge Scanning',
    isWaitingForInput: false,
    sessionActive: false,
  };

  constructor(private terminal: InteractiveTerminalService) {}

  async onModuleInit() {
    this.terminal.showBanner();
    this.terminal.showMessage(
      '🚀 Interactive simulation controller initialized',
      'success',
    );
    this.terminal.showMessage(
      'The system will guide you through each phase of the workflow',
      'info',
    );
    await this.terminal.waitForEnter('Press Enter to start the simulation');
  }

  // Event observables for mock services
  onBadgeScanned(): Observable<string> {
    return this.badgeScannedSubject.asObservable();
  }

  onRoomTimerExpired(): Observable<void> {
    return this.roomTimerExpiredSubject.asObservable();
  }

  onAllGamesComplete(): Observable<void> {
    return this.allGamesCompleteSubject.asObservable();
  }

  onScoresReceived(): Observable<GameScores> {
    return this.scoresReceivedSubject.asObservable();
  }

  // Phase control methods
  async startPhase(
    phase: number,
    title: string,
    description: string,
  ): Promise<void> {
    this.state.currentPhase = phase;
    this.state.phaseTitle = title;
    this.state.isWaitingForInput = true;

    this.terminal.showPhaseHeader(phase, title, description);

    switch (phase) {
      case 1:
        await this.handleBadgeScanningPhase();
        break;
      case 2:
        await this.handleAuthorizationPhase();
        break;
      case 3:
        await this.handleRoomAccessPhase();
        break;
      case 4:
        await this.handleArcadeSessionPhase();
        break;
      case 5:
        await this.handleScoreCollectionPhase();
        break;
      case 6:
        await this.handleResultEvaluationPhase();
        break;
      case 7:
        await this.handleScoreSubmissionPhase();
        break;
      case 8:
        await this.handleEndGameEffectsPhase();
        break;
      case 9:
        await this.handleCleanupPhase();
        break;
    }
  }

  private async handleBadgeScanningPhase(): Promise<void> {
    this.terminal.showStatus('Waiting for badge scan simulation', 'yellow');
    this.terminal.showMessage(
      "Simulate scanning a team member's NFC badge",
      'info',
    );

    const badgeId = await this.terminal.promptInput(
      'Enter badge ID to simulate scan',
      'TEAM001',
    );

    if (badgeId) {
      this.terminal.showMessage(`Badge scanned: ${badgeId}`, 'success');
      this.badgeScannedSubject.next(badgeId);
    }
  }

  private async handleAuthorizationPhase(): Promise<void> {
    this.terminal.showStatus(
      'Making real API call to backend server',
      'yellow',
    );
    this.terminal.showMessage(
      '🔗 Connecting to: https://vmi1015553.contaboserver.net:9010/api/game-manager/team-authorization',
      'info',
    );
    this.terminal.showMessage('⏳ Waiting for backend response...', 'info');

    await this.terminal.waitForEnter(
      'Real authorization API call will be made. Press Enter to continue',
    );
  }

  private async handleRoomAccessPhase(): Promise<void> {
    this.terminal.showStatus('Simulating room access control', 'blue');
    this.terminal.showMessage('🔆 Turning on room lighting...', 'info');
    this.terminal.showMessage('🚪 Opening access latch...', 'info');
    this.terminal.showMessage('📋 Displaying game instructions...', 'info');

    await this.terminal.waitForEnter(
      'Room access setup complete. Press Enter to continue',
    );
  }

  private async handleArcadeSessionPhase(): Promise<void> {
    this.terminal.showStatus('Starting arcade game session', 'blue');
    this.terminal.showMessage('🎮 Starting arcade machines...', 'info');
    this.terminal.showMessage('⏱️ Room timer and game timers started', 'info');

    this.state.sessionActive = true;

    const options = [
      'Wait for room timer to expire (5 minutes)',
      'Simulate all games completed',
      'Manually trigger session end',
    ];

    const choice = await this.terminal.promptSelect(
      'How should the session end?',
      options,
    );

    switch (choice) {
      case 0:
        this.terminal.showMessage(
          '⏰ Simulating room timer expiration...',
          'info',
        );
        setTimeout(() => {
          this.roomTimerExpiredSubject.next();
        }, 2000);
        break;
      case 1:
        this.terminal.showMessage(
          '🎮 Simulating all games completed...',
          'info',
        );
        setTimeout(() => {
          this.allGamesCompleteSubject.next();
        }, 2000);
        break;
      case 2:
        this.terminal.showMessage('🛑 Manual session end triggered', 'info');
        setTimeout(() => {
          this.allGamesCompleteSubject.next();
        }, 1000);
        break;
    }
  }

  private async handleScoreCollectionPhase(): Promise<void> {
    this.terminal.showStatus('Collecting arcade game scores', 'yellow');
    this.terminal.showGameScoreInput();

    const game1 =
      parseInt(await this.terminal.promptInput('Game 1 Score', '0'), 10) || 0;
    const game2 =
      parseInt(await this.terminal.promptInput('Game 2 Score', '0'), 10) || 0;
    const game3 =
      parseInt(await this.terminal.promptInput('Game 3 Score', '0'), 10) || 0;
    const game4 =
      parseInt(await this.terminal.promptInput('Game 4 Score', '0'), 10) || 0;

    const scores: GameScores = { game1, game2, game3, game4 };

    this.terminal.showMessage(
      `Scores entered: ${JSON.stringify(scores)}`,
      'success',
    );

    // Simulate receiving scores from arcade machines
    setTimeout(() => {
      this.scoresReceivedSubject.next(scores);
    }, 1000);
  }

  private async handleResultEvaluationPhase(): Promise<void> {
    this.terminal.showStatus('Evaluating game results', 'blue');
    this.terminal.showMessage('🎯 Calculating win/loss status...', 'info');
    this.terminal.showMessage('💰 Checking for jackpot conditions...', 'info');

    await this.terminal.waitForEnter(
      'Result evaluation complete. Press Enter to continue',
    );
  }

  private async handleScoreSubmissionPhase(): Promise<void> {
    this.terminal.showStatus('Submitting scores to backend', 'blue');
    this.terminal.showMessage(
      '📤 Sending team scores to game management API...',
      'info',
    );

    await this.terminal.waitForEnter(
      'Score submission complete. Press Enter to continue',
    );
  }

  private async handleEndGameEffectsPhase(): Promise<void> {
    this.terminal.showStatus('Playing end game effects', 'blue');

    const effectOptions = [
      '💰 Jackpot animation + celebration',
      '🏆 Win animation',
      '😞 Loss animation',
    ];

    const effect = await this.terminal.promptSelect(
      'Which effect should play?',
      effectOptions,
    );

    switch (effect) {
      case 0:
        this.terminal.showMessage(
          '💰 JACKPOT! Playing jackpot animation...',
          'success',
        );
        this.terminal.showMessage(
          '🎉 Celebration effects activated!',
          'success',
        );
        break;
      case 1:
        this.terminal.showMessage(
          '🏆 Team won! Playing win animation...',
          'success',
        );
        break;
      case 2:
        this.terminal.showMessage(
          '😞 Team lost. Playing loss animation...',
          'warning',
        );
        break;
    }

    await this.terminal.waitForEnter(
      'End game effects complete. Press Enter to continue',
    );
  }

  private async handleCleanupPhase(): Promise<void> {
    this.terminal.showStatus('Cleaning up session', 'green');
    this.terminal.showMessage('🧹 Resetting system state...', 'info');
    this.terminal.showMessage('🔒 Closing access latch...', 'info');
    this.terminal.showMessage('🔅 Turning off lighting...', 'info');
    this.terminal.showMessage('💚 Returning to waiting state...', 'info');

    this.state.sessionActive = false;
    this.state.currentTeam = undefined;

    await this.terminal.waitForEnter(
      'Session cleanup complete. Press Enter to start next session',
    );
  }

  // Utility methods
  getCurrentState(): SimulationState {
    return { ...this.state };
  }

  async triggerEmergencyStop(): Promise<void> {
    this.terminal.showMessage('🚨 EMERGENCY STOP TRIGGERED', 'error');
    this.state.sessionActive = false;
    // Trigger emergency stop in the system
  }

  cleanup(): void {
    this.terminal.cleanup();
  }
}
