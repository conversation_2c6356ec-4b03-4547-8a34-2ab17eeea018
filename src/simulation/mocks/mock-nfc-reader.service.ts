import { Injectable, Logger, Optional } from '@nestjs/common';
import { NfcReaderService } from '../../hardware/interfaces/nfc-reader.interface';
import { Subject, Observable } from 'rxjs';
import { SimulationControllerService } from '../interactive/simulation-controller.service';

@Injectable()
export class MockNfcReaderService implements NfcReaderService {
  private readonly log = new Logger(MockNfcReaderService.name);
  private readonly tag$ = new Subject<string>();

  constructor(
    @Optional() private simulationController?: SimulationControllerService,
  ) {
    if (this.simulationController) {
      // Interactive mode - controlled by simulation controller
      this.setupInteractiveMode();
    } else {
      // Fallback to simple keypress mode
      this.setupKeypressMode();
    }
  }

  onTag(): Observable<string> {
    return this.tag$.asObservable();
  }

  private setupInteractiveMode() {
    this.log.log(
      '[MOCK] NFC Reader in interactive mode - waiting for simulation controller',
    );

    // Subscribe to simulation controller badge scans
    this.simulationController?.onBadgeScanned().subscribe((badgeId) => {
      this.log.log(`[MOCK] Interactive badge scan: ${badgeId}`);
      this.tag$.next(badgeId);
    });
  }

  private setupKeypressMode() {
    this.log.log(
      '[MOCK] NFC Reader in keypress mode - press "s" to simulate badge scan',
    );
    // Keep the original keypress functionality as fallback
    const readline = require('node:readline');
    readline.emitKeypressEvents(process.stdin);
    if (process.stdin.setRawMode) process.stdin.setRawMode(true);
    process.stdin.on('keypress', (_, key) => {
      if (key.name === 's') {
        this.log.log('[MOCK] Keypress badge scan: 123456');
        this.tag$.next('123456');
      }
    });
  }
}
