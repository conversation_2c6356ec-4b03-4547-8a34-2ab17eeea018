import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import globalConfig from './config/global';
import validationSchema from './config/validation.schema';
import { HardwareModule } from './hardware/hardware.module';
import { ApiModule } from './api/api.module';
import { TeamArcadeModule } from './team-arcade/team-arcade.module';
import { SimulationModule } from './simulation/mocks/simulation.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [globalConfig],
      validationSchema,
    }),
    HardwareModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (cfg: ConfigService) => {
        const mode = cfg.get<string>('global.mode', 'SIM');
        return mode === 'PROD' ? 'PROD' : 'SIM';
      },
      inject: [ConfigService],
    }),
    SimulationModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (cfg: ConfigService) => ({
        mode: cfg.get<string>('global.mode', 'SIM'),
      }),
      inject: [ConfigService],
    }),
    ApiModule,
    TeamArcadeModule,
  ],
})
export class AppModule {}
