import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HardwareModule } from '../hardware/hardware.module';
import { ApiModule } from '../api/api.module';
import { TeamArcadeService } from './team-arcade.service';
import { InteractiveSimulationModule } from '../simulation/interactive/interactive-simulation.module';

@Module({
  imports: [
    HardwareModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (cfg: ConfigService) => {
        const mode = cfg.get<string>('global.mode', 'SIM');
        return mode === 'PROD' ? 'PROD' : 'SIM';
      },
      inject: [ConfigService],
    }),
    ApiModule,
    InteractiveSimulationModule,
  ],
  providers: [TeamArcadeService],
  exports: [TeamArcadeService],
})
export class TeamArcadeModule {}
