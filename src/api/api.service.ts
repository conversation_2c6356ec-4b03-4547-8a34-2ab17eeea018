// src/api/api.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom, timeout, catchError } from 'rxjs';
import { AxiosError } from 'axios';
import {
  TeamGameManagerResponse,
  TeamScoreRequest,
} from '../common/dto/game‑manager.dto';

@Injectable()
export class ApiService {
  private log = new Logger(ApiService.name);

  constructor(private http: HttpService, private cfg: ConfigService) {}

  /**
   * Health check to verify backend connectivity
   */
  async checkBackendHealth(): Promise<{ healthy: boolean; message: string }> {
    try {
      const url = `${this.baseUrl()}/health`;
      this.log.log('[HEALTH-CHECK] Checking backend connectivity...');

      const { data } = await firstValueFrom(
        this.http
          .get(url, {
            timeout: 5000, // 5 second timeout for health check
            headers: { Accept: 'application/json' },
          })
          .pipe(
            timeout(5000),
            catchError((error) => {
              throw error;
            }),
          ),
      );

      this.log.log('[HEALTH-CHECK] ✅ Backend is healthy');
      return { healthy: true, message: 'Backend is reachable' };
    } catch (error) {
      const message = this.getHealthCheckErrorMessage(error);
      this.log.error(`[HEALTH-CHECK] ❌ ${message}`);
      return { healthy: false, message };
    }
  }

  private getHealthCheckErrorMessage(error: any): string {
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return `Backend server unreachable at ${this.baseUrl()}`;
    }

    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
      return 'Backend server timeout - server may be overloaded';
    }

    if (error.response) {
      return `Backend returned HTTP ${error.response.status}`;
    }

    return `Backend connectivity issue: ${error.message}`;
  }

  private baseUrl() {
    return this.cfg.get<string>('global.api.baseUrl');
  }

  private handleApiError(error: any, operation: string): never {
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      this.log.error(
        `[${operation}] Backend server unreachable: ${error.message}`,
      );
      throw new Error(
        `Backend server unreachable. Please check if the server at ${this.baseUrl()} is running.`,
      );
    }

    if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
      this.log.error(`[${operation}] Request timeout: ${error.message}`);
      throw new Error(`Request timeout. The backend server may be overloaded.`);
    }

    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const message = error.response.data?.message || error.message;
      this.log.error(`[${operation}] HTTP ${status}: ${message}`);
      throw new Error(`Backend API error (${status}): ${message}`);
    }

    // Generic error
    this.log.error(`[${operation}] Unexpected error: ${error.message}`);
    throw new Error(`API communication failed: ${error.message}`);
  }

  /** ➜ GET /authorization */
  async teamAuthorization(
    badgeId: string,
    gameId: number,
  ): Promise<TeamGameManagerResponse> {
    const url = `${this.baseUrl()}/team-authorization`;
    this.log.log(
      `[TEAM-AUTH] Requesting authorization for badge ${badgeId} on game ${gameId}`,
    );

    try {
      const { data } = await firstValueFrom(
        this.http
          .get<TeamGameManagerResponse>(url, {
            params: { badgeId, gameId },
            headers: { Accept: 'application/json' },
            timeout: 10000, // 10 second timeout
          })
          .pipe(
            timeout(10000),
            catchError((error) => {
              throw error;
            }),
          ),
      );

      this.log.log(
        `[TEAM-AUTH] Response code: ${data.code}, message: ${data.message}`,
      );
      return data;
    } catch (error) {
      this.handleApiError(error, 'TEAM-AUTH');
    }
  }

  /** ➜ GET /create-score */
  async teamCreateScore(
    scoreRequest: TeamScoreRequest,
  ): Promise<TeamGameManagerResponse> {
    const url = `${this.baseUrl()}/team-create-score`;
    this.log.log(
      `[TEAM-SCORE] Submitting scores for game ${scoreRequest.gameId} with ${scoreRequest.players.length} players`,
    );

    try {
      const { data } = await firstValueFrom(
        this.http
          .post<TeamGameManagerResponse>(url, scoreRequest, {
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
            timeout: 15000, // 15 second timeout for score submission
          })
          .pipe(
            timeout(15000),
            catchError((error) => {
              throw error;
            }),
          ),
      );

      this.log.log(
        `[TEAM-SCORE] Response code: ${data.code}, message: ${data.message}`,
      );
      return data;
    } catch (error) {
      this.handleApiError(error, 'TEAM-SCORE');
    }
  }
}
