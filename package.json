{"name": "team-arcade-middleware", "version": "0.1.0", "license": "MIT", "private": true, "scripts": {"start": "node dist/main.js", "start:dev": "nest start --watch", "build": "nest build", "test": "jest", "lint": "eslint \"{src,tests}/**/*.ts\" --fix"}, "dependencies": {"@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.2.0", "@nestjs/config": "^3.1.0", "@nestjs/core": "^10.2.0", "@nestjs/platform-express": "^10.2.0", "joi": "^17.9.2", "onoff": "^6.0.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "spi-device": "^3.1.2", "nfc-pcsc": "^0.8.1", "serialport": "^9.2.4"}, "devDependencies": {"@nestjs/cli": "^10.2.0", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.2.0", "@types/jest": "^29.5.14", "@types/node": "^20.8.0", "eslint": "^8.52.0", "jest": "^29.7.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "packageManager": "yarn@1.22.19"}