# Team Arcade Middleware

A NestJS-based middleware service that acts as an intermediary between team-based arcade machines and backend game management systems.

## Description

This application serves as a middleware layer that implements a complete 9-phase team arcade game session workflow:

1. **Badge Scanning** - Waits for team member NFC badge scan
2. **Team Authorization** - Validates team access with backend API
3. **Room Access Control** - Controls lighting, access latches, and displays instructions
4. **Arcade Game Session Management** - Starts arcade machines and monitors timers
5. **Score Collection** - Receives final scores from arcade machines
6. **Result Evaluation** - Determines win/loss/jackpot status based on scores
7. **Backend Score Submission** - Submits team results to game management system
8. **End Game Effects** - Displays appropriate animations (win/loss/jackpot)
9. **Session Cleanup** - Resets system for next team

Built with [NestJS](https://github.com/nestjs/nest) framework.

## Project setup

```bash
$ npm install
```

## Configuration

The middleware can be configured through environment variables or the `src/config/global.json` file.

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# Team Arcade Middleware Configuration
PORT=3000
STATION_ID=ARCADE-01
MODE=PROD                                                    # Use PROD for production, SIM for simulation
API_BASE=https://vmi1015553.contaboserver.net:9010/api/game-manager  # Live backend server
GAME_ID=1

# Game Rules Configuration
ROOM_DURATION_MINUTES=5
MAX_GAMES_PER_SESSION=4
JACKPOT_THRESHOLD=1000
```

### Production Configuration

For production deployment, ensure the following settings in `src/config/global.json`:

```json
{
  "mode": "PROD",
  "api": {
    "baseUrl": "https://vmi1015553.contaboserver.net:9010/api/game-manager"
  }
}
```

**Important**: When `MODE=PROD`, the middleware will:

- Use real hardware services (NFC readers, Controllino sensors, LED control, serial communication)
- Connect to the live backend server for team authorization and score submission
- Disable simulation/mock services

When `MODE=SIM`, the middleware will:

- Use mock hardware services for development and testing
- Enable interactive simulation mode for manual testing
- Still connect to the real backend API (useful for API testing)

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## 🎮 Interactive Simulation Mode

For development and testing without physical hardware, the system includes a comprehensive interactive simulation mode:

```bash
# Start interactive simulation
$ npm run start:dev
```

The interactive simulation provides:

- **Step-by-step workflow control** - Manual progression through all 9 phases
- **Terminal-based interface** - Clear prompts and status displays
- **Manual input simulation** - Enter badge IDs, scores, and control decisions
- **Real-time feedback** - See exactly what happens at each phase
- **Complete testing scenarios** - Test success, failure, and edge cases

See [INTERACTIVE_SIMULATION_GUIDE.md](./INTERACTIVE_SIMULATION_GUIDE.md) for detailed usage instructions.

## API Integration

This middleware integrates with the GameManagerResource API endpoints:

### Team Authorization

- **Endpoint**: `GET /api/game-manager/team-authorization`
- **Purpose**: Authorizes a team to play a specific game
- **Parameters**: `badgeId` (string), `gameId` (number)

### Team Score Submission

- **Endpoint**: `POST /api/game-manager/team-create-score`
- **Purpose**: Submits team scores after game completion
- **Body**: JSON with `gameId` and array of player scores

For detailed API specifications, see `tasks.md`.

## Hardware Support

The middleware supports various hardware configurations:

- **NFC Readers**: PC/SC compatible readers and RS232 serial readers
- **Sensors**: Controllino-based sensor systems
- **LEDs**: RGB LED status indicators
- **Serial Control**: Room lighting, access latches, arcade machine control
- **Timer Monitoring**: Room duration and game completion tracking
- **Display Control**: Game instructions and result animations
- **Simulation Mode**: Complete mock hardware services for development

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment

### Production Deployment

1. **Configure Production Settings**:

   - Set `MODE=PROD` in environment variables or `global.json`
   - Verify backend URL points to `https://vmi1015553.contaboserver.net:9010/api/game-manager`
   - Ensure hardware devices are properly connected (NFC reader, Controllino)

2. **Build the Application**:

   ```bash
   npm run build
   ```

3. **Start in Production Mode**:

   ```bash
   npm run start:prod
   ```

4. **Verify Backend Connectivity**:
   - The middleware performs a health check on startup
   - Check logs for "✅ Backend connectivity verified" message
   - If health check fails, verify network connectivity and backend server status

### Hardware Requirements for Production

- **NFC Reader**: RS232 compatible reader connected to `/dev/ttyUSB0`
- **Controllino**: Arduino-compatible controller connected to `/dev/ttyACM0`
- **Network**: Stable internet connection for backend API communication
- **Permissions**: Ensure the application has access to serial ports

### Troubleshooting

- **Backend Connection Issues**: Check network connectivity and firewall settings
- **Hardware Not Detected**: Verify USB connections and device permissions
- **Serial Port Access**: Ensure user has permissions to access `/dev/ttyUSB0` and `/dev/ttyACM0`

## Backend API Integration

The middleware is now configured to connect to the live backend server at `https://vmi1015553.contaboserver.net:9010/api/game-manager`. All simulation/mock data has been replaced with real API calls for:

- **Team Authorization**: Validates team access and retrieves player data
- **Score Submission**: Submits final game scores and results
- **Health Monitoring**: Automatic backend connectivity verification on startup

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
