# 🎮 Interactive Simulation Guide

This guide explains how to use the interactive terminal-based simulation system for testing the team arcade middleware without physical hardware.

## 🚀 Getting Started

1. **Start the Interactive Simulation**:

   ```bash
   npm run start:dev
   ```

2. **Follow the Interactive Prompts**: The system will guide you through each of the 9 phases with clear instructions and prompts.

## 📋 9-Phase Interactive Workflow

### Phase 1: Badge Scanning 🏷️

- **What it does**: Simulates NFC badge scanning
- **Your input**: Enter a badge ID (e.g., "TEAM001", "BADGE123")
- **Default**: TEAM001
- **Example**: Type `TEAM001` and press Enter

### Phase 2: Team Authorization 🔐

- **What it does**: Makes a **real API call** to the live backend server
- **Your input**: Press Enter to proceed with the API call
- **Backend URL**: `https://vmi1015553.contaboserver.net:9010/api/game-manager/team-authorization`
- **Result**: Success/failure depends on whether the badge exists in the backend database
- **Display**: Shows real authorization result from the backend

### Phase 3: Room Access Control 🚪

- **What it does**: Simulates room lighting, access latch, and instruction display
- **Your input**: Press Enter to continue after reviewing the simulated actions
- **Actions shown**:
  - 🔆 Turning on room lighting
  - 🚪 Opening access latch
  - 📋 Displaying game instructions

### Phase 4: Arcade Game Session Management 🎮

- **What it does**: Simulates starting arcade machines and monitoring timers
- **Your input**: Choose how the session should end
- **Options**:
  1. Wait for room timer to expire (5 minutes)
  2. Simulate all games completed
  3. Manually trigger session end
- **Example**: Select option `2` for quick testing

### Phase 5: Score Collection 📊

- **What it does**: Simulates receiving scores from 4 arcade machines
- **Your input**: Enter scores for each game
- **Prompts**:
  - Game 1 Score: (enter number, e.g., 1500)
  - Game 2 Score: (enter number, e.g., 800)
  - Game 3 Score: (enter number, e.g., 0)
  - Game 4 Score: (enter number, e.g., 2000)
- **Tips**:
  - Enter 0 for games not played
  - Enter high scores (>1000) to test jackpot conditions

### Phase 6: Result Evaluation 🎯

- **What it does**: Automatically calculates win/loss and jackpot status
- **Your input**: Press Enter to continue
- **Shows**: Total score, win/loss status, jackpot determination

### Phase 7: Backend Score Submission 📤

- **What it does**: Simulates submitting scores to the backend API
- **Your input**: Press Enter to continue
- **Shows**: API submission status

### Phase 8: End Game Effects 🎭

- **What it does**: Simulates playing appropriate animations
- **Your input**: Choose which effect to play
- **Options**:
  1. 💰 Jackpot animation + celebration
  2. 🏆 Win animation
  3. 😞 Loss animation
- **Example**: Select based on the game result from Phase 6

### Phase 9: Session Cleanup 🧹

- **What it does**: Simulates resetting the system for the next team
- **Your input**: Press Enter to complete cleanup
- **Actions shown**:
  - 🧹 Resetting system state
  - 🔒 Closing access latch
  - 🔅 Turning off lighting
  - 💚 Returning to waiting state

## 🎯 Testing Scenarios

### Scenario 1: Successful Team with Jackpot

1. **Phase 1**: Enter a **valid badge ID** from your backend (e.g., existing team badge)
2. **Phase 2**: Press Enter → Real API call succeeds (✅ Team authorized)
3. **Phase 4**: Select option `2` (all games completed)
4. **Phase 5**: Enter scores: `2000, 1500, 800, 1200` (high scores)
5. **Phase 8**: Select option `1` (jackpot animation)

### Scenario 2: Team Loses (All Zeros)

1. **Phase 1**: Enter a **valid badge ID** from your backend
2. **Phase 2**: Press Enter → Real API call succeeds (✅ Team authorized)
3. **Phase 4**: Select option `2` (all games completed)
4. **Phase 5**: Enter scores: `0, 0, 0, 0` (no points)
5. **Phase 8**: Select option `3` (loss animation)

### Scenario 3: Authorization Failure

1. **Phase 1**: Enter an **invalid badge ID** (not in backend database)
2. **Phase 2**: Press Enter → Real API call fails (❌ Authorization failed)
3. **Result**: System shows access denied and returns to Phase 1

### Scenario 4: Room Timer Expiration

1. **Phase 1**: Enter a **valid badge ID** from your backend
2. **Phase 2**: Press Enter → Real API call succeeds (✅ Team authorized)
3. **Phase 4**: Select option `1` (room timer expires)
4. **Phase 5**: Enter any scores (simulates partial game)

## 🛠️ Advanced Features

### Help Commands

- Type `help` at any prompt to see available commands
- Type `status` to see current phase and system status
- Type `emergency` to trigger emergency stop
- Press `Ctrl+C` to exit the simulation

### Configuration Testing

Test different configurations by modifying `.env`:

```bash
# Test different jackpot thresholds
JACKPOT_THRESHOLD=500

# Test different room durations
ROOM_DURATION_MINUTES=3

# Test different max games
MAX_GAMES_PER_SESSION=6
```

### API Response Testing

The simulation uses the real API service, so you can test:

- Network connectivity
- API response handling
- Error scenarios
- Backend integration

## 🐛 Troubleshooting

### Common Issues

1. **Terminal Input Not Working**:

   - Make sure you're running in a proper terminal (not IDE integrated terminal)
   - Try running with `npm run start` instead of `start:dev`

2. **Simulation Stuck**:

   - Press `Ctrl+C` to exit and restart
   - Check for any error messages in the logs

3. **API Errors**:
   - Check your `API_BASE` configuration in `.env`
   - Verify backend server is running
   - Check network connectivity

### Debug Mode

Enable debug logging by setting:

```bash
LOG_LEVEL=debug
```

## 🎓 Learning the System

Use the interactive simulation to:

1. **Understand the workflow**: See how each phase connects
2. **Test edge cases**: Try different input combinations
3. **Verify integrations**: Test API calls and hardware commands
4. **Debug issues**: Isolate problems to specific phases
5. **Train team members**: Demonstrate the system behavior

## 🔄 Continuous Testing

The simulation automatically loops back to Phase 1 after Phase 9, allowing you to:

- Test multiple teams in sequence
- Verify session cleanup
- Test different scenarios back-to-back
- Simulate a full day of operations

---

**Happy Testing! 🎮**
