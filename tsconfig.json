{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strict": true, "noImplicitAny": false, "moduleResolution": "node", "resolveJsonModule": true, "types": ["node"]}, "include": ["src/**/*", "ecosystem.config.js"], "exclude": ["node_modules", "dist"]}